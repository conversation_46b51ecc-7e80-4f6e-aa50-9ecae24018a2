/**
 * NeuralOps™ Agent Framework
 * Modular AI agents for DeFiSeek blockchain intelligence
 */

// Base framework
export { ApiClient, BaseAgent, AgentRegistry, type AgentConfig } from './base/ApiClient';

// bitsCrunch Blockchain Intelligence Agents
export { default as supportedChainsAgent } from './bitcrunch/supportedChainsAgent';
export type { Blockchain, SupportedChains } from './bitcrunch/supportedChainsAgent';
export { default as walletRiskAgent } from './bitcrunch/walletRiskAgent';
export type { WalletRisk } from './bitcrunch/walletRiskAgent';

// DeFi Analysis Agents
// tokenAnalysisAgent temporarily removed for build fix

// Utilities
export {
  getSupportedChains,
  isChainSupported,
  findChain,
  getChainSuggestions,
  normalizeChainIdentifier,
  clearChainsCache,
} from './utils/chainUtils';

// Register all agents
import { AgentRegistry } from './base/ApiClient';
import supportedChainsAgent from './bitcrunch/supportedChainsAgent';
import walletRiskAgent from './bitcrunch/walletRiskAgent';
// import tokenAnalysisAgent from './defi/tokenAnalysisAgent'; // temporarily removed

// Auto-register agents when this module is imported (only once)
let isInitialized = false;

if (!isInitialized) {
  AgentRegistry.register(supportedChainsAgent);
  AgentRegistry.register(walletRiskAgent);
  // AgentRegistry.register(tokenAnalysisAgent); // temporarily removed
  isInitialized = true;

  if (process.env.NODE_ENV === 'development') {
    console.log('🤖 NeuralOps™ Agent Framework initialized');
    console.log(`📊 Registered agents: ${AgentRegistry.getAll().map(a => a.id).join(', ')}`);
  }
}
