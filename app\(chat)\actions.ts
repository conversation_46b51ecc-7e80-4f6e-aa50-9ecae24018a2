'use server';

import { CoreMessage, CoreUserMessage, generateText } from 'ai';
import { cookies } from 'next/headers';

import { customModel } from '@/neural_ops';
import { DEFAULT_MODEL_NAME } from '@/neural_ops/models';
import { getChatsByUserId } from '@/db/queries';

export async function saveModelId(model: string) {
  const cookieStore = await cookies();
  cookieStore.set('model-id', model);
}

export async function generateTitleFromUserMessage({
  message,
  userId,
}: {
  message: CoreUserMessage;
  userId: string;
}) {
  // Get existing chat titles for this user to avoid duplicates
  const existingChats = await getChatsByUserId({ id: userId });
  const existingTitles = existingChats.map((chat) => chat.title.toLowerCase());

  const { text: baseTitle } = await generateText({
    model: customModel(DEFAULT_MODEL_NAME),
    system: `You are a creative title generator for DeFi/Web3 conversations. Create engaging, unique titles that capture the essence of the conversation.

STYLE GUIDELINES:
- Keep it under 50 characters for better readability
- Use creative, memorable language that stands out
- Include relevant crypto/DeFi terminology when appropriate
- Make it sound exciting and engaging, not boring
- Use action words and compelling phrases
- Avoid generic words like "Analysis", "Discussion", "Guide"

EXISTING TITLES:
${existingTitles.map(t => `- ${t}`).join('\n')}

TONE: Exciting, engaging, slightly playful but informative
USE EMOJIS: Add 1-2 relevant emojis at the start when appropriate

Generate a creative, unique title for this message:`,
    prompt:
      typeof message.content === 'string'
        ? message.content
        : JSON.stringify(message.content),
  });

  // Clean and format the title
  let finalTitle = baseTitle
    .trim()
    .replace(/['":\[\]]/g, '') // Remove quotes, colons, brackets
    .replace(/\s+/g, ' ') // Normalize whitespace
    .slice(0, 50); // Ensure max length for better readability

  // Check for duplicates and add uniqueness if needed
  let uniqueTitle = finalTitle;
  let counter = 1;

  while (existingTitles.includes(uniqueTitle.toLowerCase())) {
    counter++;
    // Add creative suffixes that match the DeFi/Web3 theme
    const suffixes = [
      '2.0',
      'Redux',
      'Revisited',
      'Part II',
      'Extended',
      'Plus',
      'Advanced',
      'Pro',
    ];
    const suffix = suffixes[(counter - 2) % suffixes.length];
    uniqueTitle = `${finalTitle} ${suffix}`;

    // If still too long, truncate the base title
    if (uniqueTitle.length > 50) {
      const truncatedBase = finalTitle.slice(0, 50 - suffix.length - 1);
      uniqueTitle = `${truncatedBase} ${suffix}`;
    }
  }

  return uniqueTitle;
}
