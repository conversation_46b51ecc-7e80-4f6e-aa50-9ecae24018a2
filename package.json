{"name": "ai-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "tsx db/migrate & next build", "migratedb": "tsx db/migrate", "start": "next start", "lint": "next lint", "// Git Commands": "--------------------Git Commands-------------------", "git-commit-push": "git diff-index --quiet HEAD || (git add . && git commit -m \"Something just got updated!\" --allow-empty && git push origin main)", "git-commit-push-force": "git add . && git commit -m \"Something just got updated!\" --no-verify --allow-empty && git push origin main --force"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.16", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "1.0.0-canary.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/react-visually-hidden": "^1.1.0", "@vercel/analytics": "^1.3.1", "@vercel/blob": "^0.24.1", "@vercel/postgres": "^0.10.0", "ai": "4.0.0-canary.9", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "diff-match-patch": "^1.0.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.34.0", "encoding": "^0.1.13", "framer-motion": "^11.3.19", "geist": "^1.3.1", "lucide-react": "^0.446.0", "next": "15.4.5", "next-auth": "5.0.0-beta.25", "next-themes": "^0.3.0", "orderedmap": "^2.1.1", "pg": "^8.16.3", "pino-pretty": "^13.0.0", "postgres": "^3.4.4", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "react": "19.1.1", "react-dom": "19.1.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "zod": "^3.23.8"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/date-fns": "^2.6.3", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.25.0", "eslint": "^8.57.0", "eslint-config-next": "15.4.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5"}, "pnpm": {"overrides": {"@ai-sdk/provider": "1.1.3"}}}